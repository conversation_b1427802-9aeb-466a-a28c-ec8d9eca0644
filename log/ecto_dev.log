21:07:20.178 [info] QUERY ERROR queue=3.7ms
SELECT
    a.attname as column_name,
    CASE
        -- PostgreSQL Array Types (internal names start with _)
        WHEN t.typname = '_int2' THEN 'smallint[]'
        WHEN t.typname = '_int4' THEN 'integer[]'
        WHEN t.typname = '_int8' THEN 'bigint[]'
        WHEN t.typname = '_float4' THEN 'real[]'
        WHEN t.typname = '_float8' THEN 'double precision[]'
        WHEN t.typname = '_numeric' THEN 'numeric[]'
        WHEN t.typname = '_bool' THEN 'boolean[]'
        WHEN t.typname = '_text' THEN 'text[]'
        WHEN t.typname = '_varchar' THEN 'character varying[]'
        WHEN t.typname = '_bpchar' THEN 'character[]'
        WHEN t.typname = '_char' THEN 'character[]'
        WHEN t.typname = '_date' THEN 'date[]'
        WHEN t.typname = '_time' THEN 'time without time zone[]'
        WHEN t.typname = '_timetz' THEN 'time with time zone[]'
        WHEN t.typname = '_timestamp' THEN 'timestamp without time zone[]'
        WHEN t.typname = '_timestamptz' THEN 'timestamp with time zone[]'
        WHEN t.typname = '_uuid' THEN 'uuid[]'
        WHEN t.typname = '_json' THEN 'json[]'
        WHEN t.typname = '_jsonb' THEN 'jsonb[]'
        -- Standard PostgreSQL types
        WHEN t.typname = 'int2' THEN 'smallint'
        WHEN t.typname = 'int4' THEN 'integer'
        WHEN t.typname = 'int8' THEN 'bigint'
        WHEN t.typname = 'float4' THEN 'real'
        WHEN t.typname = 'float8' THEN 'double precision'
        WHEN t.typname = 'bpchar' THEN 'character'
        WHEN t.typname = 'varchar' THEN 'character varying'
        WHEN t.typname = 'bool' THEN 'boolean'
        -- Keep standard PostgreSQL type names as-is
        ELSE t.typname
    END as data_type,
    CASE WHEN a.attnotnull THEN 'NO' ELSE 'YES' END as is_nullable,
    pg_get_expr(ad.adbin, ad.adrelid) as column_default,
    CASE
        WHEN pk.attname IS NOT NULL THEN true
        ELSE false
    END as is_primary_key
FROM pg_attribute a
JOIN pg_type t ON a.atttypid = t.oid
JOIN pg_class c ON a.attrelid = c.oid
JOIN pg_namespace n ON c.relnamespace = n.oid
LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
LEFT JOIN (
    SELECT a.attname
    FROM pg_index i
    JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
    JOIN pg_class c ON i.indrelid = c.oid
    WHERE c.relname = $1 AND i.indisprimary
) pk ON pk.attname = a.attname
WHERE c.relname = $1
    AND n.nspname = 'public'
    AND a.attnum > 0
    AND NOT a.attisdropped
ORDER BY a.attnum
 ["users"]
