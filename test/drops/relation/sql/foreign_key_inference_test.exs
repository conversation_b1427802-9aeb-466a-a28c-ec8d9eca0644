defmodule Drops.Relation.SQL.ForeignKeyInferenceTest do
  use Drops.RelationCase, async: false

  alias Drops.Relation.SQL.Inference
  alias Drops.Relation.Schema

  describe "foreign key inference from database constraints" do
    test "infers simple foreign key from SQLite database" do
      # Create parent table
      Ecto.Adapters.SQL.query!(
        Drops.Repos.Sqlite,
        """
        CREATE TABLE test_fk_parent (
          id INTEGER PRIMARY KEY,
          name TEXT NOT NULL
        )
        """,
        []
      )

      # Create child table with foreign key constraint
      Ecto.Adapters.SQL.query!(
        Drops.Repos.Sqlite,
        """
        CREATE TABLE test_fk_child (
          id INTEGER PRIMARY KEY,
          parent_id INTEGER NOT NULL,
          title TEXT NOT NULL,
          FOREIGN KEY (parent_id) REFERENCES test_fk_parent(id)
        )
        """,
        []
      )

      # Test schema inference
      schema = Inference.infer_from_table("test_fk_child", Drops.Repos.Sqlite)

      assert %Schema{} = schema
      assert schema.source == "test_fk_child"

      # Check that foreign key is inferred
      assert length(schema.foreign_keys) == 1
      fk = hd(schema.foreign_keys)
      assert fk.field == :parent_id
      assert fk.references_table == "test_fk_parent"
      assert fk.references_field == :id
    end

    @tag adapter: :postgres
    test "infers simple foreign key from PostgreSQL database" do
      # Create parent table
      Ecto.Adapters.SQL.query!(
        Drops.Repos.Postgres,
        """
        CREATE TABLE test_fk_parent_pg (
          id SERIAL PRIMARY KEY,
          name TEXT NOT NULL
        )
        """,
        []
      )

      # Create child table with foreign key constraint
      Ecto.Adapters.SQL.query!(
        Drops.Repos.Postgres,
        """
        CREATE TABLE test_fk_child_pg (
          id SERIAL PRIMARY KEY,
          parent_id INTEGER NOT NULL,
          title TEXT NOT NULL,
          FOREIGN KEY (parent_id) REFERENCES test_fk_parent_pg(id)
        )
        """,
        []
      )

      # Test schema inference
      schema = Inference.infer_from_table("test_fk_child_pg", Drops.Repos.Postgres)

      assert %Schema{} = schema
      assert schema.source == "test_fk_child_pg"

      # Check that foreign key is inferred
      assert length(schema.foreign_keys) == 1
      fk = hd(schema.foreign_keys)
      assert fk.field == :parent_id
      assert fk.references_table == "test_fk_parent_pg"
      assert fk.references_field == :id
    end

    test "infers multiple foreign keys from SQLite database" do
      # Create multiple parent tables
      Ecto.Adapters.SQL.query!(
        Drops.Repos.Sqlite,
        """
        CREATE TABLE test_fk_users (
          id INTEGER PRIMARY KEY,
          name TEXT NOT NULL
        )
        """,
        []
      )

      Ecto.Adapters.SQL.query!(
        Drops.Repos.Sqlite,
        """
        CREATE TABLE test_fk_categories (
          id INTEGER PRIMARY KEY,
          name TEXT NOT NULL
        )
        """,
        []
      )

      # Create child table with multiple foreign keys
      Ecto.Adapters.SQL.query!(
        Drops.Repos.Sqlite,
        """
        CREATE TABLE test_fk_posts (
          id INTEGER PRIMARY KEY,
          user_id INTEGER NOT NULL,
          category_id INTEGER NOT NULL,
          title TEXT NOT NULL,
          FOREIGN KEY (user_id) REFERENCES test_fk_users(id),
          FOREIGN KEY (category_id) REFERENCES test_fk_categories(id)
        )
        """,
        []
      )

      # Test schema inference
      schema = Inference.infer_from_table("test_fk_posts", Drops.Repos.Sqlite)

      assert %Schema{} = schema
      assert schema.source == "test_fk_posts"

      # Check that both foreign keys are inferred
      assert length(schema.foreign_keys) == 2

      user_fk = Enum.find(schema.foreign_keys, &(&1.field == :user_id))
      assert user_fk != nil
      assert user_fk.references_table == "test_fk_users"
      assert user_fk.references_field == :id

      category_fk = Enum.find(schema.foreign_keys, &(&1.field == :category_id))
      assert category_fk != nil
      assert category_fk.references_table == "test_fk_categories"
      assert category_fk.references_field == :id
    end

    test "infers composite foreign key from SQLite database" do
      # Create parent table with composite primary key
      Ecto.Adapters.SQL.query!(
        Drops.Repos.Sqlite,
        """
        CREATE TABLE test_fk_composite_parent (
          tenant_id INTEGER NOT NULL,
          user_id INTEGER NOT NULL,
          name TEXT NOT NULL,
          PRIMARY KEY (tenant_id, user_id)
        )
        """,
        []
      )

      # Create child table with composite foreign key
      Ecto.Adapters.SQL.query!(
        Drops.Repos.Sqlite,
        """
        CREATE TABLE test_fk_composite_child (
          id INTEGER PRIMARY KEY,
          parent_tenant_id INTEGER NOT NULL,
          parent_user_id INTEGER NOT NULL,
          title TEXT NOT NULL,
          FOREIGN KEY (parent_tenant_id, parent_user_id) REFERENCES test_fk_composite_parent(tenant_id, user_id)
        )
        """,
        []
      )

      # Test schema inference
      schema = Inference.infer_from_table("test_fk_composite_child", Drops.Repos.Sqlite)

      assert %Schema{} = schema
      assert schema.source == "test_fk_composite_child"

      # Check that composite foreign key is inferred
      # Note: Current implementation may only handle the first column of composite FK
      assert length(schema.foreign_keys) >= 1

      # Find the foreign key that references the composite parent
      composite_fk =
        Enum.find(
          schema.foreign_keys,
          &(&1.references_table == "test_fk_composite_parent")
        )

      assert composite_fk != nil
      assert composite_fk.field in [:parent_tenant_id, :parent_user_id]
    end

    test "infers foreign key with UUID types from SQLite database" do
      # Create parent table with UUID primary key
      Ecto.Adapters.SQL.query!(
        Drops.Repos.Sqlite,
        """
        CREATE TABLE test_fk_uuid_parent (
          id UUID PRIMARY KEY,
          name TEXT NOT NULL
        )
        """,
        []
      )

      # Create child table with UUID foreign key
      Ecto.Adapters.SQL.query!(
        Drops.Repos.Sqlite,
        """
        CREATE TABLE test_fk_uuid_child (
          id UUID PRIMARY KEY,
          parent_id UUID NOT NULL,
          title TEXT NOT NULL,
          FOREIGN KEY (parent_id) REFERENCES test_fk_uuid_parent(id)
        )
        """,
        []
      )

      # Test schema inference
      schema = Inference.infer_from_table("test_fk_uuid_child", Drops.Repos.Sqlite)

      assert %Schema{} = schema
      assert schema.source == "test_fk_uuid_child"

      # Check that foreign key is inferred
      assert length(schema.foreign_keys) == 1
      fk = hd(schema.foreign_keys)
      assert fk.field == :parent_id
      assert fk.references_table == "test_fk_uuid_parent"
      assert fk.references_field == :id

      # Check that the foreign key field has correct type
      parent_id_field = Enum.find(schema.fields, &(&1.name == :parent_id))
      assert parent_id_field != nil
      assert parent_id_field.ecto_type == Ecto.UUID
    end

    test "handles table with no foreign keys" do
      # Create a simple table with no foreign keys
      Ecto.Adapters.SQL.query!(
        Drops.Repos.Sqlite,
        """
        CREATE TABLE test_fk_no_fks (
          id INTEGER PRIMARY KEY,
          name TEXT NOT NULL,
          email TEXT UNIQUE
        )
        """,
        []
      )

      # Test schema inference
      schema = Inference.infer_from_table("test_fk_no_fks", Drops.Repos.Sqlite)

      assert %Schema{} = schema
      assert schema.source == "test_fk_no_fks"

      # Check that no foreign keys are inferred
      assert length(schema.foreign_keys) == 0
    end

    test "infers foreign key with ON DELETE and ON UPDATE actions" do
      # Create parent table
      Ecto.Adapters.SQL.query!(
        Drops.Repos.Sqlite,
        """
        CREATE TABLE test_fk_actions_parent (
          id INTEGER PRIMARY KEY,
          name TEXT NOT NULL
        )
        """,
        []
      )

      # Create child table with foreign key actions
      Ecto.Adapters.SQL.query!(
        Drops.Repos.Sqlite,
        """
        CREATE TABLE test_fk_actions_child (
          id INTEGER PRIMARY KEY,
          parent_id INTEGER NOT NULL,
          title TEXT NOT NULL,
          FOREIGN KEY (parent_id) REFERENCES test_fk_actions_parent(id) ON DELETE CASCADE ON UPDATE RESTRICT
        )
        """,
        []
      )

      # Test schema inference
      schema = Inference.infer_from_table("test_fk_actions_child", Drops.Repos.Sqlite)

      assert %Schema{} = schema
      assert schema.source == "test_fk_actions_child"

      # Check that foreign key is inferred
      assert length(schema.foreign_keys) == 1
      fk = hd(schema.foreign_keys)
      assert fk.field == :parent_id
      assert fk.references_table == "test_fk_actions_parent"
      assert fk.references_field == :id
    end
  end

  describe "foreign key inference from existing migration tables" do
    @tag relations: [:associations]
    test "infers foreign keys from associations table" do
      # Test with the associations table created by migration
      schema = Inference.infer_from_table("associations", Drops.Repos.Sqlite)

      assert %Schema{} = schema
      assert schema.source == "associations"

      # Should infer the parent_id foreign key
      parent_fk = Enum.find(schema.foreign_keys, &(&1.field == :parent_id))
      assert parent_fk != nil
      assert parent_fk.references_table == "association_parents"
      assert parent_fk.references_field == :id
    end

    @tag relations: [:associations]
    test "infers foreign keys from association_items table" do
      # Test with the association_items table created by migration
      schema = Inference.infer_from_table("association_items", Drops.Repos.Sqlite)

      assert %Schema{} = schema
      assert schema.source == "association_items"

      # Should infer the association_id foreign key
      assoc_fk = Enum.find(schema.foreign_keys, &(&1.field == :association_id))
      assert assoc_fk != nil
      assert assoc_fk.references_table == "associations"
      assert assoc_fk.references_field == :id
    end

    @tag relations: [:binary_id_tables]
    test "infers foreign keys from binary_id tables" do
      # Test with binary_id_users table that has organization_id foreign key
      schema = Inference.infer_from_table("binary_id_users", Drops.Repos.Sqlite)

      assert %Schema{} = schema
      assert schema.source == "binary_id_users"

      # Should infer the organization_id foreign key
      org_fk = Enum.find(schema.foreign_keys, &(&1.field == :organization_id))
      assert org_fk != nil
      assert org_fk.references_table == "binary_id_organizations"
      assert org_fk.references_field == :id
    end
  end
end
