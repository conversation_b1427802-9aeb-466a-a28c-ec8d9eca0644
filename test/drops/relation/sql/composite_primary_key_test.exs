defmodule Drops.Relation.SQL.CompositePrimaryKeyTest do
  use Drops.RelationCase, async: false

  alias Drops.Relation.SQL.Inference
  alias Drops.Relation.Schema
  alias Drops.Relation.Schema.PrimaryKey

  describe "composite primary key inference" do
    test "infers composite primary key from SQLite database" do
      # Create table with composite primary key
      Ecto.Adapters.SQL.query!(
        Drops.Repos.Sqlite,
        """
        CREATE TABLE test_composite_pk (
          tenant_id INTEGER NOT NULL,
          user_id INTEGER NOT NULL,
          name TEXT NOT NULL,
          email TEXT,
          PRIMARY KEY (tenant_id, user_id)
        )
        """,
        []
      )

      # Test schema inference
      schema = Inference.infer_from_table("test_composite_pk", Drops.Repos.Sqlite)

      assert %Schema{} = schema
      assert schema.source == "test_composite_pk"

      # Check that composite primary key is inferred
      assert %PrimaryKey{} = schema.primary_key
      assert length(schema.primary_key.fields) == 2

      # Check primary key field names
      pk_field_names = Enum.map(schema.primary_key.fields, & &1.name)
      assert :tenant_id in pk_field_names
      assert :user_id in pk_field_names

      # Check that schema recognizes it as composite
      assert Schema.composite_primary_key?(schema)

      # Check that primary key fields have correct metadata
      tenant_id_field = Enum.find(schema.primary_key.fields, &(&1.name == :tenant_id))
      user_id_field = Enum.find(schema.primary_key.fields, &(&1.name == :user_id))

      assert tenant_id_field != nil
      assert user_id_field != nil
      # Primary key integer fields become :id type
      assert tenant_id_field.ecto_type == :id
      # Primary key integer fields become :id type
      assert user_id_field.ecto_type == :id

      # Check that primary key fields are also marked in the main fields list
      tenant_id_main_field = Enum.find(schema.fields, &(&1.name == :tenant_id))
      user_id_main_field = Enum.find(schema.fields, &(&1.name == :user_id))

      assert tenant_id_main_field != nil
      assert user_id_main_field != nil
      assert Map.get(tenant_id_main_field.meta, :is_primary_key, false) == true
      assert Map.get(user_id_main_field.meta, :is_primary_key, false) == true
    end

    @tag adapter: :postgres
    test "infers composite primary key from PostgreSQL database" do
      # Create table with composite primary key
      Ecto.Adapters.SQL.query!(
        Drops.Repos.Postgres,
        """
        CREATE TABLE test_composite_pk_pg (
          tenant_id INTEGER NOT NULL,
          user_id INTEGER NOT NULL,
          name TEXT NOT NULL,
          email TEXT,
          PRIMARY KEY (tenant_id, user_id)
        )
        """,
        []
      )

      # Test schema inference
      schema = Inference.infer_from_table("test_composite_pk_pg", Drops.Repos.Postgres)

      assert %Schema{} = schema
      assert schema.source == "test_composite_pk_pg"

      # Check that composite primary key is inferred
      assert %PrimaryKey{} = schema.primary_key
      assert length(schema.primary_key.fields) == 2

      # Check primary key field names
      pk_field_names = Enum.map(schema.primary_key.fields, & &1.name)
      assert :tenant_id in pk_field_names
      assert :user_id in pk_field_names

      # Check that schema recognizes it as composite
      assert Schema.composite_primary_key?(schema)
    end

    test "infers three-column composite primary key from SQLite database" do
      # Create table with three-column composite primary key
      Ecto.Adapters.SQL.query!(
        Drops.Repos.Sqlite,
        """
        CREATE TABLE test_triple_pk (
          region_id INTEGER NOT NULL,
          tenant_id INTEGER NOT NULL,
          user_id INTEGER NOT NULL,
          name TEXT NOT NULL,
          PRIMARY KEY (region_id, tenant_id, user_id)
        )
        """,
        []
      )

      # Test schema inference
      schema = Inference.infer_from_table("test_triple_pk", Drops.Repos.Sqlite)

      assert %Schema{} = schema
      assert schema.source == "test_triple_pk"

      # Check that composite primary key is inferred
      assert %PrimaryKey{} = schema.primary_key
      assert length(schema.primary_key.fields) == 3

      # Check primary key field names
      pk_field_names = Enum.map(schema.primary_key.fields, & &1.name)
      assert :region_id in pk_field_names
      assert :tenant_id in pk_field_names
      assert :user_id in pk_field_names

      # Check that schema recognizes it as composite
      assert Schema.composite_primary_key?(schema)
    end

    test "infers composite primary key with mixed types from SQLite database" do
      # Create table with composite primary key using different types
      Ecto.Adapters.SQL.query!(
        Drops.Repos.Sqlite,
        """
        CREATE TABLE test_mixed_pk (
          tenant_code TEXT NOT NULL,
          user_id INTEGER NOT NULL,
          name TEXT NOT NULL,
          PRIMARY KEY (tenant_code, user_id)
        )
        """,
        []
      )

      # Test schema inference
      schema = Inference.infer_from_table("test_mixed_pk", Drops.Repos.Sqlite)

      assert %Schema{} = schema
      assert schema.source == "test_mixed_pk"

      # Check that composite primary key is inferred
      assert %PrimaryKey{} = schema.primary_key
      assert length(schema.primary_key.fields) == 2

      # Check primary key field types
      tenant_code_field = Enum.find(schema.primary_key.fields, &(&1.name == :tenant_code))
      user_id_field = Enum.find(schema.primary_key.fields, &(&1.name == :user_id))

      assert tenant_code_field != nil
      assert user_id_field != nil
      # String fields remain :string
      assert tenant_code_field.ecto_type == :string
      # Primary key integer fields become :id type
      assert user_id_field.ecto_type == :id

      # Check that schema recognizes it as composite
      assert Schema.composite_primary_key?(schema)
    end

    test "handles table with no primary key correctly" do
      # Create table without primary key
      Ecto.Adapters.SQL.query!(
        Drops.Repos.Sqlite,
        """
        CREATE TABLE test_no_pk_table (
          name TEXT NOT NULL,
          email TEXT,
          data TEXT
        )
        """,
        []
      )

      # Test schema inference with default primary key
      schema =
        Inference.infer_from_table("test_no_pk_table", Drops.Repos.Sqlite,
          default_primary_key: [:name]
        )

      assert %Schema{} = schema
      assert schema.source == "test_no_pk_table"

      # Check that default primary key is applied
      assert %PrimaryKey{} = schema.primary_key
      assert length(schema.primary_key.fields) == 1

      pk_field = hd(schema.primary_key.fields)
      assert pk_field.name == :name

      # Should not be considered composite
      refute Schema.composite_primary_key?(schema)
    end
  end

  describe "composite primary key inference from existing migration tables" do
    @tag relations: [:composite_pk]
    test "infers composite primary key from composite_pk table" do
      # Test with the composite_pk table created by migration
      schema = Inference.infer_from_table("composite_pk", Drops.Repos.Sqlite)

      assert %Schema{} = schema
      assert schema.source == "composite_pk"

      # Check primary key - this might be limited by SQLite introspection
      # SQLite PRAGMA table_info may only show the first primary key column
      assert %PrimaryKey{} = schema.primary_key

      # The current implementation might only detect the first column
      # due to SQLite limitations, but we should at least get that
      assert length(schema.primary_key.fields) >= 1

      first_pk_field = hd(schema.primary_key.fields)
      assert first_pk_field.name in [:part1, :part2]
    end
  end
end
