defmodule Drops.Relation.SQL.Database do
  @moduledoc """
  Database introspection data structures for SQL databases.

  This module provides a comprehensive set of structs for representing
  database metadata extracted through introspection. These structs are
  designed to be database-agnostic and provide a unified interface for
  working with table, column, index, and constraint information.

  ## Structs

  - `Table` - Complete table metadata including columns, keys, and indexes
  - `Column` - Individual column metadata with type and constraint information
  - `PrimaryKey` - Primary key constraint information (supports composite keys)
  - `ForeignKey` - Foreign key constraint information (supports composite keys)
  - `Index` - Index metadata including type and uniqueness information

  ## Usage

  These structs are typically created by database introspection implementations
  and then converted to `Drops.Relation.Schema` structs using the inference
  protocol.

  ## Examples

      # Create a simple table structure
      alias Drops.Relation.SQL.Database.{Table, Column, PrimaryKey, ForeignKey, Index}

      columns = [
        Column.new("id", "integer", false, nil, true),
        Column.new("email", "varchar(255)", true, nil, false),
        Column.new("user_id", "integer", false, nil, false)
      ]

      primary_key = PrimaryKey.new(["id"])

      foreign_keys = [
        ForeignKey.simple("user_id", "users")
      ]

      indexes = [
        Index.new("idx_posts_email", ["email"], true, :btree)
      ]

      table = Table.new("posts", columns, primary_key, foreign_keys, indexes)

      # Access table metadata
      Table.column_names(table)              # ["id", "email", "user_id"]
      Table.primary_key_column_names(table)  # ["id"]
      Table.foreign_key_column_names(table)  # ["user_id"]

      # Check column properties
      Table.primary_key_column?(table, "id")     # true
      Table.foreign_key_column?(table, "user_id") # true

      # Get specific structures
      email_column = Table.get_column(table, "email")
      user_fk = Table.get_foreign_key_for_column(table, "user_id")
  """

  # Re-export all the database structs for convenience
  alias Drops.Relation.SQL.Database.{Table, Column, PrimaryKey, ForeignKey, Index}

  defdelegate new_table(name, columns, primary_key, foreign_keys, indexes), to: Table, as: :new
  defdelegate table_from_introspection(name, columns, foreign_keys \\ [], indexes \\ []), to: Table, as: :from_introspection

  defdelegate new_column(name, type, nullable, default, primary_key, check_constraints \\ []), to: Column, as: :new
  defdelegate column_from_introspection(data), to: Column, as: :from_introspection

  defdelegate new_primary_key(columns), to: PrimaryKey, as: :new
  defdelegate primary_key_from_columns(columns), to: PrimaryKey, as: :from_columns

  defdelegate new_foreign_key(name, columns, referenced_table, referenced_columns, on_delete \\ nil, on_update \\ nil), to: ForeignKey, as: :new
  defdelegate simple_foreign_key(column, referenced_table, referenced_column \\ "id"), to: ForeignKey, as: :simple

  defdelegate new_index(name, columns, unique, type, where_clause \\ nil), to: Index, as: :new
  defdelegate index_from_introspection(data), to: Index, as: :from_introspection

  @doc """
  Creates a complete table structure from introspection data.

  This is a convenience function that creates all the necessary structs
  from raw introspection data.

  ## Parameters

  - `table_name` - The name of the table
  - `column_data` - List of maps with column introspection data
  - `foreign_key_data` - List of maps with foreign key introspection data (optional)
  - `index_data` - List of maps with index introspection data (optional)

  ## Examples

      iex> column_data = [
      ...>   %{name: "id", type: "integer", not_null: true, primary_key: true, default: nil},
      ...>   %{name: "email", type: "varchar(255)", not_null: false, primary_key: false, default: nil}
      ...> ]
      iex> table = Drops.Relation.SQL.Database.create_table_from_introspection("users", column_data)
      iex> table.name
      "users"
      iex> length(table.columns)
      2
  """
  @spec create_table_from_introspection(String.t(), [map()], [map()], [map()]) :: Table.t()
  def create_table_from_introspection(table_name, column_data, foreign_key_data \\ [], index_data \\ []) do
    columns = Enum.map(column_data, &Column.from_introspection/1)
    foreign_keys = Enum.map(foreign_key_data, &create_foreign_key_from_data/1)
    indexes = Enum.map(index_data, &Index.from_introspection/1)

    Table.from_introspection(table_name, columns, foreign_keys, indexes)
  end

  # Private helper to create foreign key from introspection data
  defp create_foreign_key_from_data(data) when is_map(data) do
    name = Map.get(data, :name) || Map.get(data, "name")
    columns = Map.get(data, :columns, []) || Map.get(data, "columns", [])
    referenced_table = Map.get(data, :referenced_table) || Map.get(data, "referenced_table")
    referenced_columns = Map.get(data, :referenced_columns, []) || Map.get(data, "referenced_columns", [])
    on_delete = Map.get(data, :on_delete) || Map.get(data, "on_delete")
    on_update = Map.get(data, :on_update) || Map.get(data, "on_update")

    ForeignKey.new(name, columns, referenced_table, referenced_columns, on_delete, on_update)
  end
end
