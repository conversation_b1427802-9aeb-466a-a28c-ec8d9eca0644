defmodule Drops.Relation.SQL.Inference do
  @moduledoc """
  Unified schema inference implementation for database table introspection.

  This module consolidates all schema inference logic into a single, reusable
  implementation that can be used by both runtime schema inference (for dynamic
  relation modules) and code generation (for explicit relation files).

  The module provides a single source of truth for:
  - Database table introspection
  - Type conversion from database types to Ecto types
  - Field metadata extraction
  - Primary key detection
  - Index extraction
  - Schema struct creation

  ## Usage

      # Create schema from database table
      schema = Drops.Relation.SQL.Inference.infer_from_table("users", MyApp.Repo)

      # Create schema with custom options
      schema = Drops.Relation.SQL.Inference.infer_from_table("users", MyApp.Repo,
        include_indices: true,
        include_timestamps: false
      )
  """

  alias Drops.Relation.Schema
  alias Drops.Relation.Schema.{Field, PrimaryKey, Indices}
  alias Drops.Relation.SQL.{Introspector, DatabaseInference}

  require Logger

  @doc """
  Infers a complete Drops.Relation.Schema from a database table.

  This is the main entry point for schema inference. It performs database
  introspection and creates a complete Schema struct with all metadata.

  ## Parameters

  - `table_name` - The database table name to introspect
  - `repo` - The Ecto repository module for database access
  - `opts` - Optional configuration (see options below)

  ## Options

  - `:include_indices` - Whether to extract index information (default: true)
  - `:include_timestamps` - Whether to include timestamp fields (default: true)
  - `:default_primary_key` - Default primary key when none found (default: [:id])

  ## Returns

  A `Drops.Relation.Schema.t()` struct containing all inferred metadata.

  ## Examples

      iex> schema = Drops.Relation.SQL.Inference.infer_from_table("users", MyApp.Repo)
      iex> schema.source
      "users"
      iex> length(schema.fields)
      5
  """
  @spec infer_from_table(String.t(), module(), keyword()) :: Schema.t()
  def infer_from_table(table_name, repo, opts \\ []) do
    include_indices = Keyword.get(opts, :include_indices, true)
    _include_timestamps = Keyword.get(opts, :include_timestamps, true)

    # Use the new database introspection to get complete table metadata
    case Introspector.introspect_table(repo, table_name) do
      {:ok, table} ->
        # Convert the database table struct to a schema using the protocol
        schema = DatabaseInference.to_schema(table)

        # Apply filtering options and handle default primary key
        filtered_schema = apply_filtering_options(schema, opts)
        schema_with_pk = apply_default_primary_key(filtered_schema, opts)

        # Ensure indices are included/excluded based on options
        final_schema =
          if include_indices do
            schema_with_pk
          else
            %{schema_with_pk | indices: Indices.new([])}
          end

        final_schema

      {:error, reason} ->
        # Fallback to the old method if new introspection fails
        Logger.warning(
          "New introspection failed for table #{table_name}: #{inspect(reason)}. Falling back to legacy method."
        )

        infer_from_table_legacy(table_name, repo, opts)
    end
  end

  @doc """
  Normalizes Ecto types to their base types.

  ## Parameters

  - `ecto_type` - The Ecto type to normalize

  ## Returns

  The normalized Ecto type.

  ## Examples

      iex> Drops.Relation.SQL.Inference.normalize_ecto_type(:id)
      :integer
      iex> Drops.Relation.SQL.Inference.normalize_ecto_type(:string)
      :string
  """
  @spec normalize_ecto_type(atom() | tuple()) :: atom() | tuple()
  def normalize_ecto_type(ecto_type) do
    case ecto_type do
      :id -> :integer
      :binary_id -> :binary
      Ecto.UUID -> :binary
      {:array, inner_type} -> {:array, normalize_ecto_type(inner_type)}
      other -> other
    end
  end

  # Private helper functions

  # Apply filtering options to the schema
  defp apply_filtering_options(schema, opts) do
    include_timestamps = Keyword.get(opts, :include_timestamps, true)

    if include_timestamps do
      schema
    else
      # Filter out timestamp fields
      filtered_fields =
        Enum.reject(schema.fields, fn field ->
          field.name in [:inserted_at, :updated_at, :created_at, :modified_at]
        end)

      %{schema | fields: filtered_fields}
    end
  end

  # Apply default primary key if no primary key is found
  defp apply_default_primary_key(schema, opts) do
    default_primary_key = Keyword.get(opts, :default_primary_key, [:id])

    # Check if schema has any primary key fields
    if length(schema.primary_key.fields) == 0 do
      # Create default primary key fields
      default_pk_fields =
        Enum.map(default_primary_key, fn field_name ->
          # Try to find the field in the schema fields
          existing_field = Enum.find(schema.fields, &(&1.name == field_name))

          if existing_field do
            existing_field
          else
            # Create a default field
            Field.new(field_name, :integer, :id, field_name)
          end
        end)

      new_primary_key = PrimaryKey.new(default_pk_fields)
      %{schema | primary_key: new_primary_key}
    else
      schema
    end
  end

  # Legacy method for backward compatibility
  defp infer_from_table_legacy(table_name, repo, opts) do
    include_indices = Keyword.get(opts, :include_indices, true)
    include_timestamps = Keyword.get(opts, :include_timestamps, true)
    default_primary_key = Keyword.get(opts, :default_primary_key, [:id])

    # Introspect table columns and types using the old Introspector
    columns = Introspector.introspect_table_columns(repo, table_name)

    # Create the Drops.Relation.Schema from the introspected data
    create_schema_from_columns(
      table_name,
      columns,
      repo,
      include_indices: include_indices,
      include_timestamps: include_timestamps,
      default_primary_key: default_primary_key
    )
  end

  # Creates a Drops.Relation.Schema from introspected column data
  defp create_schema_from_columns(table_name, columns, repo, opts) do
    include_indices = Keyword.get(opts, :include_indices, true)
    include_timestamps = Keyword.get(opts, :include_timestamps, false)
    default_primary_key = Keyword.get(opts, :default_primary_key, [:id])

    # Extract primary key fields from columns
    primary_key_fields =
      columns
      |> Enum.filter(& &1.primary_key)
      |> Enum.map(&String.to_atom(&1.name))
      |> case do
        # Use default primary key when none found
        [] -> default_primary_key
        fields -> fields
      end

    # Convert columns to Field structs
    # Note: For Drops.Relation.Schema, we include ALL fields, including default :id
    # The filtering is only for Ecto schema generation, not for Drops schema
    fields =
      columns
      |> maybe_filter_timestamps(include_timestamps)
      |> Enum.map(&create_field_from_column(&1, repo))

    # No need to add default :id field since we're including all columns
    all_fields = fields

    # Create primary key with proper Field structs
    # Handle the case where default :id primary key was filtered out
    primary_key_field_structs =
      case Enum.filter(all_fields, fn field -> field.name in primary_key_fields end) do
        [] when primary_key_fields == [:id] ->
          # Default :id primary key was filtered out, create a minimal Field struct for it
          [Field.new(:id, :integer, :id, :id)]

        found_fields ->
          found_fields
      end

    primary_key = PrimaryKey.new(primary_key_field_structs)

    # Extract indices from database if requested
    indices =
      if include_indices and repo do
        case extract_indices_from_db(repo, table_name) do
          {:ok, indices} -> indices
          {:error, _} -> Indices.new()
        end
      else
        Indices.new()
      end

    # Create the schema struct (without associations as requested)
    Schema.new(
      table_name,
      primary_key,
      # foreign_keys - cannot be inferred from database structure alone
      [],
      all_fields,
      indices,
      # virtual_fields - cannot be inferred from database structure alone
      []
    )
  end

  # Filters out timestamp fields if not included
  defp maybe_filter_timestamps(columns, true), do: columns

  defp maybe_filter_timestamps(columns, false) do
    Enum.reject(columns, fn column ->
      column.name in ["inserted_at", "updated_at"]
    end)
  end

  # Creates a Field struct from a column map
  defp create_field_from_column(column, repo) do
    field_name = String.to_atom(column.name)
    base_ecto_type = Introspector.db_type_to_ecto_type(repo, column.type, column.name)

    # Use appropriate types for primary key columns
    # No longer rely on naming conventions for foreign key detection
    ecto_type =
      cond do
        column.primary_key and base_ecto_type == :binary_id ->
          :binary_id

        # Handle Ecto.UUID type for SQLite UUID primary keys
        column.primary_key and base_ecto_type == Ecto.UUID ->
          Ecto.UUID

        # For primary key integer fields, use :id type
        column.primary_key and base_ecto_type == :integer ->
          :id

        true ->
          base_ecto_type
      end

    # Extract metadata from column introspection
    meta = %{
      is_nullable: Map.get(column, :is_nullable),
      default: Map.get(column, :default),
      check_constraints: Map.get(column, :check_constraints, [])
    }

    Field.new(
      field_name,
      normalize_ecto_type(ecto_type),
      ecto_type,
      field_name,
      meta
    )
  end

  # Extracts indices from database
  defp extract_indices_from_db(repo, table_name) do
    try do
      # Use the new Introspector module
      Introspector.get_table_indices(repo, table_name)
    rescue
      _ -> {:error, :introspection_failed}
    end
  end
end
